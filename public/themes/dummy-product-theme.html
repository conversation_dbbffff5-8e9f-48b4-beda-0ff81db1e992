<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dummy Product Theme</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; }
        .header, .footer { background: #222; color: #fff; padding: 16px; }
        .hero { background: #eee; padding: 32px; text-align: center; }
        .carousel { background: #f5f5f5; padding: 16px; display: flex; gap: 16px; justify-content: center; }
        .carousel-item { text-align: center; }
        .products { display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px; padding: 32px; }
        .product { background: #fff; border: 1px solid #ddd; padding: 16px; text-align: center; }
        .footer-sections { display: flex; justify-content: space-between; margin-bottom: 8px; }
        .social-icons { display: flex; gap: 8px; }
        @media (max-width: 768px) {
            .products { grid-template-columns: 1fr; }
            .footer-sections { flex-direction: column; gap: 8px; }
        }
        .bottom-menu { position: fixed; bottom: 0; left: 0; right: 0; background: #222; color: #fff; display: flex; justify-content: space-around; padding: 12px 0; }
        .mobile-hide { display: block; }
        @media (max-width: 768px) {
            .footer { display: none; }
            .bottom-menu { display: flex; }
            .mobile-hide { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <span><strong>Brand Logo</strong></span> | <span>Store Name</span>
    </div>
    <div class="hero">
        <h2>Hero Section: Image Slider/Banner</h2>
    </div>
    <div class="carousel">
        <div class="carousel-item">
            <img src="https://via.placeholder.com/40" alt="Cat1"><br>Category 1
        </div>
        <div class="carousel-item">
            <img src="https://via.placeholder.com/40" alt="Cat2"><br>Category 2
        </div>
        <div class="carousel-item">
            <img src="https://via.placeholder.com/40" alt="Cat3"><br>Category 3
        </div>
        <div class="carousel-item">
            <img src="https://via.placeholder.com/40" alt="Cat4"><br>Category 4
        </div>
    </div>
    <div class="products">
        <div class="product">Product 1</div>
        <div class="product">Product 2</div>
        <div class="product">Product 3</div>
        <div class="product">Product 4</div>
        <div class="product">Product 5</div>
        <div class="product">Product 6</div>
        <div class="product">Product 7</div>
        <div class="product">Product 8</div>
    </div>
    <div class="footer mobile-hide">
        <div class="footer-sections">
            <div>About Us</div>
            <div>Contact Us</div>
            <div class="social-icons">
                <span>YT</span>
                <span>FB</span>
                <span>IG</span>
            </div>
        </div>
        <div>&copy; 2025 Copyright Title</div>
    </div>
    <div class="bottom-menu" style="display:none;">
        <span>Home</span>
        <span>Category</span>
        <span>Contact Us</span>
    </div>
    <script>
        // Show bottom menu on mobile
        if(window.innerWidth <= 768){
            document.querySelector('.bottom-menu').style.display = 'flex';
        }
    </script>
</body>
</html>
