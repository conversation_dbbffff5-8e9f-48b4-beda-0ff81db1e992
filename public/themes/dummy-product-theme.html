<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern E-commerce Store</title>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        .header {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px 0;
        }

        .brand-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .brand-logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }

        .store-name {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .hero p {
            font-size: 20px;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .hero-image {
            display: none;
            width: 100%;
            height: 200px;
            background: url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80') center/cover;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        /* Category Carousel */
        .category-section {
            background: white;
            padding: 40px 0;
        }

        .section-title {
            text-align: center;
            font-size: 32px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 40px;
        }

        .category-carousel {
            display: flex;
            gap: 30px;
            justify-content: center;
            flex-wrap: wrap;
            overflow-x: auto;
            padding: 0 20px;
        }

        .category-carousel::-webkit-scrollbar {
            display: none;
        }

        .category-item {
            text-align: center;
            cursor: pointer;
            padding: 20px;
            border-radius: 12px;
            background: #f8f9fa;
            border: 2px solid transparent;
            min-width: 120px;
        }

        .category-item:hover {
            background: #e9ecef;
            border-color: #667eea;
        }

        .category-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .category-name {
            font-size: 16px;
            font-weight: 500;
            color: #2c3e50;
        }

        /* Products Section */
        .products-section {
            padding: 60px 0;
            background: #f8f9fa;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            cursor: pointer;
        }

        .product-card:hover {
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .product-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }

        .product-info {
            padding: 20px;
        }

        .product-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .product-description {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .product-price {
            font-size: 20px;
            font-weight: 700;
            color: #667eea;
        }

        /* Footer */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 50px 0 30px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 30px;
        }

        .footer-section h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ecf0f1;
        }

        .footer-section p {
            color: #bdc3c7;
            line-height: 1.6;
        }

        .social-icons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .social-icon {
            width: 40px;
            height: 40px;
            background: #34495e;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-weight: bold;
        }

        .social-icon:hover {
            background: #667eea;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #34495e;
            color: #bdc3c7;
        }

        /* Bottom Menu for Mobile */
        .bottom-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e9ecef;
            display: none;
            z-index: 100;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .bottom-menu-items {
            display: flex;
            justify-content: space-around;
            padding: 15px 0;
        }

        .bottom-menu-item {
            text-align: center;
            color: #6c757d;
            font-size: 12px;
            cursor: pointer;
        }

        .bottom-menu-item.active {
            color: #667eea;
        }

        .bottom-menu-icon {
            font-size: 20px;
            margin-bottom: 5px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            .header-content {
                padding: 10px 0;
            }

            .store-name {
                font-size: 20px;
            }

            .brand-logo {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .hero {
                padding: 20px 0;
            }

            .hero h1 {
                font-size: 24px;
                margin-bottom: 15px;
            }

            .hero p {
                font-size: 14px;
                margin-bottom: 20px;
            }

            .hero-image {
                display: block;
            }

            .section-title {
                font-size: 20px;
                margin-bottom: 20px;
            }

            .category-carousel {
                gap: 15px;
                justify-content: flex-start;
                flex-wrap: nowrap;
                padding: 0 15px;
            }

            .category-item {
                min-width: 80px;
                padding: 10px;
                flex-shrink: 0;
            }

            .category-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
                margin-bottom: 8px;
            }

            .category-name {
                font-size: 12px;
            }

            .products-grid {
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }

            .product-card {
                border-radius: 8px;
            }

            .product-image {
                height: 120px;
                font-size: 12px;
            }

            .product-info {
                padding: 12px;
            }

            .product-name {
                font-size: 14px;
                margin-bottom: 5px;
            }

            .product-description {
                font-size: 12px;
                margin-bottom: 8px;
            }

            .product-price {
                font-size: 16px;
            }

            .footer {
                display: none;
            }

            .bottom-menu {
                display: block;
            }

            body {
                padding-bottom: 70px;
            }
        }

        @media (min-width: 769px) {
            .bottom-menu {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="brand-section">
                    <div class="brand-logo">S</div>
                    <div class="store-name">Modern Store</div>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-image"></div>
                <h1>Welcome to Our Store</h1>
                <p>Discover amazing products with unbeatable quality and prices. Your satisfaction is our priority.</p>
            </div>
        </div>
    </section>

    <!-- Category Section -->
    <section class="category-section">
        <div class="container">
            <h2 class="section-title">Shop by Category</h2>
            <div class="category-carousel">
                <div class="category-item">
                    <div class="category-icon">📱</div>
                    <div class="category-name">Electronics</div>
                </div>
                <div class="category-item">
                    <div class="category-icon">👕</div>
                    <div class="category-name">Fashion</div>
                </div>
                <div class="category-item">
                    <div class="category-icon">🏠</div>
                    <div class="category-name">Home & Garden</div>
                </div>
                <div class="category-item">
                    <div class="category-icon">📚</div>
                    <div class="category-name">Books</div>
                </div>
                <div class="category-item">
                    <div class="category-icon">🎮</div>
                    <div class="category-name">Gaming</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
        <div class="container">
            <h2 class="section-title">Featured Products</h2>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-info">
                        <div class="product-name">Premium Wireless Headphones</div>
                        <div class="product-description">High-quality sound with noise cancellation technology</div>
                        <div class="product-price">$199.99</div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-info">
                        <div class="product-name">Smart Fitness Watch</div>
                        <div class="product-description">Track your health and fitness goals with style</div>
                        <div class="product-price">$299.99</div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-info">
                        <div class="product-name">Organic Cotton T-Shirt</div>
                        <div class="product-description">Comfortable and sustainable fashion choice</div>
                        <div class="product-price">$29.99</div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-info">
                        <div class="product-name">Professional Camera Lens</div>
                        <div class="product-description">Capture stunning photos with crystal clear quality</div>
                        <div class="product-price">$599.99</div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-info">
                        <div class="product-name">Ergonomic Office Chair</div>
                        <div class="product-description">Comfortable seating for long work sessions</div>
                        <div class="product-price">$399.99</div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-info">
                        <div class="product-name">Bluetooth Speaker</div>
                        <div class="product-description">Portable speaker with amazing sound quality</div>
                        <div class="product-price">$89.99</div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-info">
                        <div class="product-name">Stainless Steel Water Bottle</div>
                        <div class="product-description">Keep your drinks at the perfect temperature</div>
                        <div class="product-price">$24.99</div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-info">
                        <div class="product-name">LED Desk Lamp</div>
                        <div class="product-description">Adjustable lighting for your workspace</div>
                        <div class="product-price">$49.99</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>About Us</h3>
                    <p>We are committed to providing high-quality products and exceptional customer service. Your satisfaction is our top priority.</p>
                </div>
                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <p>Email: <EMAIL><br>
                    Phone: +****************<br>
                    Address: 123 Commerce St, City, State 12345</p>
                </div>
                <div class="footer-section">
                    <h3>Follow Us</h3>
                    <p>Stay connected with us on social media</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon">YT</a>
                        <a href="#" class="social-icon">FB</a>
                        <a href="#" class="social-icon">IG</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Modern Store. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bottom Menu for Mobile -->
    <div class="bottom-menu">
        <div class="bottom-menu-items">
            <div class="bottom-menu-item active">
                <div class="bottom-menu-icon">🏠</div>
                <div>Home</div>
            </div>
            <div class="bottom-menu-item">
                <div class="bottom-menu-icon">📂</div>
                <div>Category</div>
            </div>
            <div class="bottom-menu-item">
                <div class="bottom-menu-icon">📞</div>
                <div>Contact</div>
            </div>
        </div>
    </div>

    <script>
        // Handle responsive behavior
        function handleResize() {
            const bottomMenu = document.querySelector('.bottom-menu');
            if (window.innerWidth <= 768) {
                bottomMenu.style.display = 'block';
            } else {
                bottomMenu.style.display = 'none';
            }
        }

        // Initial check
        handleResize();

        // Listen for window resize
        window.addEventListener('resize', handleResize);

        // Handle bottom menu item clicks
        document.querySelectorAll('.bottom-menu-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.bottom-menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
